language: node_js
node_js:
- 8
- 10
env:
  global:
  - NODE_ENV=production
  matrix:
  - NPM_SCRIPT="tap:unit -- --jobs=4"
  - NPM_SCRIPT="tap:integration -- --jobs=4"
cache:
  directories:
  - "$HOME/.npm"
before_install:
# package-lock.json was introduced in npm@5
- '[[ $(node -v) =~ ^v9.*$ ]] || npm install -g npm@latest' # skipped when using node 9
- npm install -g greenkeeper-lockfile
install:
- if [[ ${TRAVIS_BRANCH:0:11} = greenkeeper ]]; then npm install --production=false; else npm ci --production=false; fi
before_script: greenkeeper-lockfile-update
script: npm run $NPM_SCRIPT
after_script: greenkeeper-lockfile-upload
jobs:
    include:
    - env: NPM_SCRIPT=lint
      node_js: 8
    - env: NPM_SCRIPT=build
      node_js: 8
      if: not (branch in (master, develop) and type != pull_request)
    - stage: release
      node_js: 8
      env: NPM_SCRIPT=build
      before_deploy:
      - npm --no-git-tag-version version $($(npm bin)/json -f package.json version)-prerelease.$(date +%Y%m%d%H%M%S)
      - git config --global user.email "$(git log --pretty=format:"%ae" -n1)"
      - git config --global user.name "$(git log --pretty=format:"%an" -n1)"
      deploy:
      - provider: npm
        on:
          all_branches: true
          condition: $TRAVIS_EVENT_TYPE != cron
        skip_cleanup: true
        email: $NPM_EMAIL
        api_key: $NPM_TOKEN
      - provider: script
        on:
          all_branches: true
          condition: $TRAVIS_EVENT_TYPE != cron
        skip_cleanup: true
        script: npm run --silent deploy -- -x -r $GH_PAGES_REPO
      - provider: script
        on:
          branch: develop
          condition: $TRAVIS_EVENT_TYPE == cron
        skip_cleanup: true
        script: npm run i18n:src && npm run i18n:push
stages:
- test
- name: release
  if: branch in (master, develop) and type != pull_request
