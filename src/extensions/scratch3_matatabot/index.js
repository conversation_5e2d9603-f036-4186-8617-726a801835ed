/* eslint-disable max-len */
/* eslint-disable no-mixed-operators */
/* eslint-disable prefer-const */
/* eslint-disable key-spacing */
/* eslint-disable camelcase */
/* eslint-disable no-console */
const ArgumentType = require('../../extension-support/argument-type');
const BlockType = require('../../extension-support/block-type');
// const log = require('../../util/log');
const cast = require('../../util/cast');
const MathUtil = require('../../util/math-util');
const formatMessage = require('format-message');
const BLE = require('../../io/ble');
const Base64Util = require('../../util/base64-util');

/**
 * Icon png to be displayed at the left edge of each extension block, encoded as a data URI.
 * @type {string}
 */
const blockIconURI = 'data:image/png;base64,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'

const BLECommand = {
    CMD_CHECK_VERSION:    0x01,
    CMD_MOVE_POS:         0x10,
    CMD_MOVE_SPEED:       0x11,
    CMD_DANCE:            0x12,
    CMD_ACTION:           0x13,
    CMD_PLAY_TONE:        0x15,
    CMD_PLAY_MUSIC:       0x16,
    CMD_EYE_LED:          0x17,
    CMD_SET_NEW_PROTOCOL: 0x7e,
    CMD_HEARTBEAT:        0x87,
    CMD_GENERAL_RSP:      0x88
};

const MovePosCommand = {
    MOVE_FORWARD:         0x01,
    MOVE_BACKWARD:        0x02,
    MOVE_LEFT:            0x03,
    MOVE_RIGHT:           0x04
};

const MoveSpeedCommand = {
    MOTION_LEFT:          0x01,
    MOTION_RIGHT:         0x02,
    MOTION_BOTH:          0x03
};

const PalyMusicCommand = {
    PLAY_UNTIL_DONE:      0x01,
    PLAY_NORMAL:          0x02,
    PLAY_LOOP:            0x03,
    PLAY_STOP:            0x04
};

const MATATABOT_LATEST_FIRMWARE_VERSION = '2.2.14'; // 最新固件在这里设置

/**
 * A time interval to wait (in milliseconds) before reporting to the BLE socket
 * that data has stopped coming from the peripheral.
 */
// const BLETimeout = 4500;

/**
 * A time interval to wait (in milliseconds) while a block that sends a BLE message is running.
 * @type {number}
 */
// const BLESendInterval = 1000;

/**
 * A string to report to the BLE socket when the matatabot has stopped receiving data.
 * @type {string}
 */
// const BLEDataStoppedError = 'matatabot extension stopped receiving data';

const BLEINFO = {
    service:'6e400001-b5a3-f393-e0a9-e50e24dcca9e',
    rxChar: '6e400003-b5a3-f393-e0a9-e50e24dcca9e',
    txChar: '6e400002-b5a3-f393-e0a9-e50e24dcca9e',
    name:   'MatataBot',
    namePrefix: 'MatataBot'
};

/**
 * Manage communication with a MatataBot peripheral over a Scrath Link client socket.
 */
class MatataBot {
    /**
     * Construct a MatataBot communication object.
     * @param {Runtime} runtime - the Scratch 3.0 runtime
     * @param {string} extensionId - the id of the extension
     */
    constructor (runtime, extensionId) {

        /**
         * The Scratch 3.0 runtime used to trigger the green flag button.
         * @type {Runtime}
         * @private
         */
        this._runtime = runtime;

        /**
         * The BluetoothLowEnergy connection socket for reading/writing peripheral data.
         * @type {BLE}
         * @private
         */
        this._ble = null;
        this._runtime.registerPeripheralExtension(extensionId, this);

        /**
         * The id of the extension this peripheral belongs to.
         */
        this._extensionId = extensionId;

        /**
         * Interval ID for data reading timeout.
         * @type {number}
         * @private
         */
        this._timeoutID = null;

        /**
         * A flag that is true while we are busy sending data to the BLE socket.
         * @type {boolean}
         * @private
         */
        this._busy = false;

        /**
         * ID for a timeout which is used to clear the busy flag if it has been
         * true for a long time.
         */
        this._busyTimeoutID = null;

        /**
         * Processing for receiving data frames
         */
        this._receivedCommand = new Array();
        this._receivedCommandStart = false;
        this._receivedCommandLength = 0;
        this._lastFrameReservedData = null;
        this.device = 'MatataBot';
        this.version = new Array(0, 0, 0);

        this.commandSyncFlag = {
            motionForwardStepFlag: false,
            motionBackwardStepFlag: false,
            motionTurnLeftAngleFlag: false,
            motionTurnRightAngleFlag: false,
            motionMovingFlag: false,
            motionWhirlFlag: false,
            motionWheelPowerFlag: false,
            motionWheelSpeedFlag: false,
            motionStopMovingFlag: false,
            doDanceFlag: false,
            doActionFlag: false,
            soundAltoFlag: false,
            soundTrebleFlag: false,
            soundMelodyFlag: false,
            soundSongFlag: false,
            eyeLedSingleSet1Flag: false,
            eyeLedSingleSet2Flag: false,
            eyeLedSingleSet3Flag: false,
            eyeLedAllOffFlag: false,
            setNewProtocolFlag: false,
            getVersionFlag: false
        };


        this.reset = this.reset.bind(this);
        this._onConnect = this._onConnect.bind(this);
        this._onMessage = this._onMessage.bind(this);
    }

    /**
     * Called by the runtime when user wants to scan for a peripheral.
     */
    scan () {
        console.log('matatabot start scan...');
        if (this._ble) {
            this._ble.disconnect();
            console.log('can not search ble device');
        }
        this._ble = new BLE(this._runtime, this._extensionId, {
            filters: [
                {services: [BLEINFO.service]},
                {namePrefix: BLEINFO.namePrefix}
                // {name: BLEINFO.name},
            ]
            // optionalServices: [BLEINFO.service]
        }, this._onConnect, this.reset);
    }

    /**
     * Called by the runtime when user wants to connect to a certain peripheral.
     * @param {number} id - the id of the peripheral to connect to.
     */
    connect (id) {
        if (this._ble) {
            this._ble.connectPeripheral(id);
        }
    }

    /**
     * Disconnect from the matatabot.
     */
    disconnect () {
        if (this._ble) {
            console.log('matatabot disconnect');
            this._ble.disconnect();
        }

        this.reset();
    }

    /**
     * Reset all the state and timeout/interval ids.
     */
    reset () {
        if (this._timeoutID) {
            window.clearTimeout(this._timeoutID);
            this._timeoutID = null;
        }
    }

    /**
     * Return true if connected to the matatabot.
     * @return {boolean} - whether the matatabot is connected.
     */
    isConnected () {
        let connected = false;
        if (this._ble) {
            connected = this._ble.isConnected();
        }
        return connected;
    }

    /**
     * Send a message to the peripheral BLE socket.
     * @param {Uint8Array} message - the message to write
     */
    send (message) {
        if (!this.isConnected()) return;
        if (this._busy) return;
        // Set a busy flag so that while we are sending a message and waiting for
        // the response, additional messages are ignored.
        this._busy = true;

        // Set a timeout after which to reset the busy flag. This is used in case
        // a BLE message was sent for which we never received a response, because
        // e.g. the peripheral was turned off after the message was sent. We reset
        // the busy flag after a while so that it is possible to try again later.
        this._busyTimeoutID = window.setTimeout(() => {
            this._busy = false;
        }, 3000);
        const output = new Uint8Array(message.length);
        for (let i = 0; i < message.length; i++) {
            output[i] = message[i];
        }
        const data = Base64Util.uint8ArrayToBase64(output);

        this._ble.write(BLEINFO.service, BLEINFO.txChar, data, 'base64', false).then(
            () => {
                this._busy = false;
                window.clearTimeout(this._busyTimeoutID);
            }
        );
    }

    crc16 (buffer, crc_init) {
        let crc = crc_init & 0xffff;
        for (let i = 0; i < buffer.length; i++) {
            crc = ((crc >> 8) | (crc << 8)) & 0xffff;
            crc ^= buffer[i] & 0xffff;
            crc ^= ((crc & 0xff) >> 4) & 0xffff;
            crc ^= ((crc << 8) << 4) & 0xffff;
            crc ^= (((crc & 0xff) << 4) << 1) & 0xffff;
        }
        return crc;
    }

    packCommand (command_data) {
        let command_array = new Array();
        let message_len = command_data.length + 2;
        const message_len_array = new Array();
        message_len_array.push(message_len);
        let crc = this.crc16(message_len_array, 0xffff);
        crc = this.crc16(command_data, crc);
        command_array.push(0xfe);
        command_array.push(message_len);
        for (let i = 0; i < command_data.length; i++) {
            if (command_data[i] === 0xfe) {
                command_array.push(0xfd);
                command_array.push(0xde);
            } else if (command_data[i] === 0xfd) {
                command_array.push(0xfd);
                command_array.push(0xdd);
            } else {
                command_array.push(command_data[i]);
            }
        }
        if ((crc >> 8) === 0xfe) {
            command_array.push(0xfd);
            command_array.push(0xde);
        } else if ((crc >> 8) === 0xfd) {
            command_array.push(0xfd);
            command_array.push(0xdd);
        } else {
            command_array.push(crc >> 8);;
        }
        if ((crc & 0xff) === 0xfe) {
            command_array.push(0xfd);
            command_array.push(0xde);
        } else if ((crc & 0xff) === 0xfd) {
            command_array.push(0xfd);
            command_array.push(0xdd);
        } else {
            command_array.push(crc & 0xff);
        }
        let log_string = "send: ";
        for (let i = 0; i < command_array.length; i++) {
            log_string = log_string + "0x" + command_array[i].toString(16) + ",";
        }
        console.log(log_string);
        return command_array;   
    }

    depackCommand (command_data) {
        let command_data_temp = new Array();
        if (this._lastFrameReservedData !== null) {
            command_data_temp.push(this._lastFrameReservedData);
            this._lastFrameReservedData = null;
        }
        for (let i = 0; i < command_data.length; i++) {
            command_data_temp.push(command_data[i]);
        }
        for (let i = 0; i < command_data_temp.length; i++) {
            if ((command_data_temp[i] === 0xfe) && (this._receivedCommandStart === false)){
                this._receivedCommand.push(0xfe);
                this._receivedCommandStart = true;
            } else if (this._receivedCommandStart === true) {
                if (command_data_temp[i] === 0xfd){
                    if (i === command_data_temp.length) {
                        this._lastFrameReservedData = 0xfd;
                        continue;
                    } else if (command_data_temp[i + 1] === 0xdd) {
                        this._receivedCommand.push(0xfd);
                        i++;
                        continue;
                    } else if (command_data_temp[i + 1] === 0xde) {
                        this._receivedCommand.push(0xfe);
                        i++;
                        continue;
                    }
                    this._receivedCommand.push(0xfd);
                } else {
                    this._receivedCommand.push(command_data_temp[i]);
                }
            }
        }
        let log_string = "receive: ";
        for (let i = 0; i < this._receivedCommand.length; i++) {
            log_string = log_string + "0x" + this._receivedCommand[i].toString(16) + ",";
        }
        console.log(log_string);
        if (this._receivedCommand.length > 3) {
            this._receivedCommandLength = this._receivedCommand[1] & 0xff;
            // console.log(this._receivedCommandLength);
        }
        if (this._receivedCommand.length >= this._receivedCommandLength + 2) {
            this.parseCommand();
        }
    }

    checkCRC () {
        let crc_data_temp = this._receivedCommand.slice(1, this._receivedCommandLength);
        let crc_calculation = this.crc16(crc_data_temp, 0xffff);
        // eslint-disable-next-line max-len
        let crc_received = (this._receivedCommand[this._receivedCommandLength] << 8) & 0xff00 | ((this._receivedCommand[this._receivedCommandLength + 1]) & 0xff);
        if (crc_calculation === crc_received) {
            return true;
        }
        return false;
    }

    clearCommandSyncFlag () {
        if (this.commandSyncFlag.motionForwardStepFlag === true) {
            this.commandSyncFlag.motionForwardStepFlag = false;
        }
        if (this.commandSyncFlag.motionBackwardStepFlag === true) {
            this.commandSyncFlag.motionBackwardStepFlag = false;
        }
        if (this.commandSyncFlag.motionTurnLeftAngleFlag === true) {
            this.commandSyncFlag.motionTurnLeftAngleFlag = false;
        }
        if (this.commandSyncFlag.motionTurnRightAngleFlag === true) {
            this.commandSyncFlag.motionTurnRightAngleFlag = false;
        }
        if (this.commandSyncFlag.motionMovingFlag === true) {
            this.commandSyncFlag.motionMovingFlag = false;
        }
        if (this.commandSyncFlag.motionWhirlFlag === true) {
            this.commandSyncFlag.motionWhirlFlag = false;
        }
        if (this.commandSyncFlag.motionWheelPowerFlag === true) {
            this.commandSyncFlag.motionWheelPowerFlag = false;
        }
        if (this.commandSyncFlag.motionWheelSpeedFlag === true) {
            this.commandSyncFlag.motionWheelSpeedFlag = false;
        }
        if (this.commandSyncFlag.motionStopMovingFlag === true) {
            this.commandSyncFlag.motionStopMovingFlag = false;
        }
        if (this.commandSyncFlag.doDanceFlag === true) {
            this.commandSyncFlag.doDanceFlag = false;
        }
        if (this.commandSyncFlag.doActionFlag === true) {
            this.commandSyncFlag.doActionFlag = false;
        }
        if (this.commandSyncFlag.soundAltoFlag === true) {
            this.commandSyncFlag.soundAltoFlag = false;
        }
        if (this.commandSyncFlag.soundTrebleFlag === true) {
            this.commandSyncFlag.soundTrebleFlag = false;
        }
        if (this.commandSyncFlag.soundMelodyFlag === true) {
            this.commandSyncFlag.soundMelodyFlag = false;
        }
        if (this.commandSyncFlag.soundSongFlag === true) {
            this.commandSyncFlag.soundSongFlag = false;
        }
        if (this.commandSyncFlag.eyeLedSingleSet1Flag === true) {
            this.commandSyncFlag.eyeLedSingleSet1Flag = false;
        }
        if (this.commandSyncFlag.eyeLedSingleSet2Flag === true) {
            this.commandSyncFlag.eyeLedSingleSet2Flag = false;
        }
        if (this.commandSyncFlag.eyeLedSingleSet3Flag === true) {
            this.commandSyncFlag.eyeLedSingleSet3Flag = false;
        }
        if (this.commandSyncFlag.eyeLedAllOffFlag === true) {
            this.commandSyncFlag.eyeLedAllOffFlag = false;
        }
    }

    parseCommand () {
        if (this.checkCRC() === false) {
            console.log('checkCRC false!');
            this._receivedCommand = this._receivedCommand.slice(this._receivedCommandLength + 2);
            this._receivedCommandLength = 0;
            return;
        }
        let command_data = this._receivedCommand.slice(1, this._receivedCommandLength);
        switch (command_data[1]) {
            case BLECommand.CMD_SET_NEW_PROTOCOL: {
                console.log('set new protocol response!');
                this.commandSyncFlag.setNewProtocolFlag = false;
                break;
            }
            case BLECommand.CMD_GENERAL_RSP: {
                console.log('get general response!');
                this.clearCommandSyncFlag();
                break;
            }
            case BLECommand.CMD_CHECK_VERSION: {
                this.version[0] = command_data[3];
                this.version[1] = command_data[4];
                this.version[2] = command_data[5];
                let version = command_data[3] + '.' + command_data[4] + '.' + command_data[5];
                console.log('version:' + version);
                this.commandSyncFlag.getVersionFlag = false;
                break;
            }
            case BLECommand.CMD_HEARTBEAT: {
                if (command_data[2] === 0x02) {
                    this.device = 'MatataCon';
                } else {
                    this.device = 'MatataBot';
                }
                break;
            }
            default: {
                break;
            }
        }

        this._receivedCommand = this._receivedCommand.slice(this._receivedCommandLength + 2);
        this._receivedCommandLength = 0;
        this._receivedCommandStart = false;
    }


    Uint8ArrayToString(fileData){
        var dataString = "";
        for (var i = 0; i < fileData.length; i++) {
        dataString += String.fromCharCode(fileData[i]);
        }
    
        return dataString
    }

    setNewProtocol (callback) {
        const setNewProtocolData = new Array();
        setNewProtocolData.push(BLECommand.CMD_SET_NEW_PROTOCOL);
        setNewProtocolData.push(0x02);
        setNewProtocolData.push(0x02);
        setNewProtocolData.push(0x00);
        setNewProtocolData.push(0x00);
        this.commandSyncFlag.setNewProtocolFlag = true;
        this.send(this.packCommand(setNewProtocolData));
        return new Promise(resolve => {
            let count = 0;
            let interval = setInterval(() => {
                if (count > 4000) {
                    console.log('setNewProtocol timeout!');
                    clearInterval(interval);
                    this.commandSyncFlag.setNewProtocolFlag = false;
                    matata.showFirmwareModal(this.device, 'unknown');
                    this.disconnect();
                    callback && callback(false);
                    resolve(false);
                } else if (this.commandSyncFlag.setNewProtocolFlag === false) {
                    console.log('setNewProtocol success!');
                    clearInterval(interval);
                    callback && callback(true);
                    resolve(true);
                }
                count += 100;
            }, 100);
        });
    }

    checkVersion () {
        const cmd = [BLECommand.CMD_CHECK_VERSION, 0x01];
        this.send(this.packCommand(cmd));
        this.commandSyncFlag.getVersionFlag = true;
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 1000) {
                    console.log('checkVersion timeout!');
                    this.commandSyncFlag.getVersionFlag = false;
                    matata.showFirmwareModal(this.device, "unknown");
                    this.disconnect();
                    clearInterval(interval);
                    resolve(false);
                } else if (this.commandSyncFlag.getVersionFlag === false) {
                    const version = this.version[0] + '.' + this.version[1] + '.' + this.version[2];
                    const version_array =  MATATABOT_LATEST_FIRMWARE_VERSION.split(".");
                    const latest_version = (parseInt(version_array[0], 10) << 16) + (parseInt(version_array[1], 10) << 8) + parseInt(version_array[2], 10);
                    const current_version = (this.version[0] << 16) + (this.version[1] << 8) + this.version[2];
                    console.log(latest_version);
                    console.log(current_version);
                    if (latest_version > current_version) {
                        matata.showFirmwareModal(this.device, version);
                        this.disconnect();
                    }
                    clearInterval(interval);
                    resolve(true);
                }
                count += 10;
            }, 10);
        });
    }

    /**
     * Starts reading data from peripheral after BLE has connected to it.
     * @private
     */
    _onConnect () {
        this._ble.startNotifications(BLEINFO.service, BLEINFO.rxChar, this._onMessage);
        this.setNewProtocol((state) => {
            if (state) {
                this.checkVersion()
            }
        });
    }

    /**
     * Process the sensor data from the incoming BLE characteristic.
     * @param {object} base64 - the incoming BLE data.
     * @private
     */
    _onMessage (base64) {
        // parse data
        const dataReceived = Base64Util.base64ToUint8Array(base64);
        // console.log("matataBot recv:");
        // console.log(dataReceived);
        this.depackCommand(dataReceived);
    }
}


/**
 * Enum for motor port names.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const MotionStepMenu = {
    STEP1: '1 step',
    STEP2: '2 steps',
    STEP3: '3 step3',
    STEP4: '4 steps',
    STEP5: '5 steps',
    STEP6: '6 steps',
    ROLL_DICE: 'roll dice'
};

/**
 * Enum for motor angle.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const MotionAngleMenu = {
    ANGLE30: '30°',
    ANGLE36: '36°',
    ANGLE45: '45°',
    ANGLE60: '60°',
    ANGLE72: '72°',
    ANGLE90: '90°',
    ANGLE108: '108°',
    ANGLE120: '120°',
    ANGLE135: '135°',
    ANGLE144: '144°',
    ANGLE150: '150°',
    ANGLE180: '180°'
};

/**
 * Enum for motor trend.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const MotionTrendMenu = {
    FORWARD: 'forward',
    BACKWARD: 'backward'
};

/**
 * Enum for motor gear.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const MotionGearMenu = {
    GEAR1: 'gear 1',
    GEAR2: 'gear 2',
    GEAR3: 'gear 3',
    GEAR4: 'gear 4',
    GEAR5: 'gear 5',
    GEAR6: 'gear 6',
    STOP:  'stop',
    NULL:  'unchanged',
    ROLL_DICE: 'roll dice'
};

/**
 * Enum for motor wheel.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const MotionWheelMenu = {
    BOTH: 'both wheels',
    LEFT: 'left wheel',
    RIGHT: 'right wheel'
};

/**
 * Enum for dance index.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const DanceIndexMenu = {
    NUM1: 'the 1st',
    NUM2: 'the 2nd',
    NUM3: 'the 3rd',
    NUM4: 'the 4th',
    NUM5: 'the 5th',
    NUM6: 'the 6th',
    ROLL_DICE: 'roll dice'
};

/**
 * Enum for action index.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const ActionIndexMenu = {
    NUM1: 'the 1st',
    NUM2: 'the 2nd',
    NUM3: 'the 3rd',
    NUM4: 'the 4th',
    NUM5: 'the 5th',
    NUM6: 'the 6th',
    ROLL_DICE: 'roll dice'
};

/**
 * Enum for motor sound beat.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const SoundBeatMenu = {
    ONE_FOURTH:   '1/4',
    TWO_FOURTH:   '2/4',
    THREE_FOURTH: '3/4',
    FOUR_FOURTH:  '4/4',
    FIVE_FOURTH:  '5/4',
    SIX_FOURTH:   '6/4',
    ROLL_DICE:    'roll dice'
};

/**
 * Enum for motor sound tone.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const SoundToneMenu = {
    DO:  'do',
    RE:  're',
    MI:  'mi',
    FA:  'fa',
    SO: 'so',
    LA:  'la',
    TI:  'ti'
};

/**
 * Enum for motor port names.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const noteFreq = {
    C3:  131,
    D3:  147,
    E3:  165,
    F3:  175,
    G3:  196,
    A3:  220,
    B3:  247,
    C4:  262,
    D4:  294,
    E4:  330,
    F4:  349,
    G4:  392,
    A4:  440,
    B4:  494
};

/**
 * Enum for sound melody.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const SoundMelodyMenu = {
    NUM1: 'the 1st',
    NUM2: 'the 2nd',
    NUM3: 'the 3rd',
    NUM4: 'the 4th',
    NUM5: 'the 5th',
    NUM6: 'the 6th',
    NUM7: 'the 7th',
    NUM8: 'the 8th',
    NUM9: 'the 9th',
    NUM10: 'the 10th',
};

/**
 * Enum for sound song.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const SoundSongMenu = {
    NUM1: 'the 1st',
    NUM2: 'the 2nd',
    NUM3: 'the 3rd',
    NUM4: 'the 4th',
    NUM5: 'the 5th',
    NUM6: 'the 6th',
    ROLL_DICE: 'roll dice'
};

/**
 * Enum for eye side.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const LooksSideMenu = {
    LEFT: 'left',
    RIGHT: 'right',
    ALL: 'all'
};

/**
 * Enum for color type.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const ColorTypeMenu = {
    WHITE:         'white',
    RED:           'red',
    YELLOW:        'yellow',
    GREEN:         'green',
    BLUE:          'blue',
    PURPLE:        'purple',
    BLACK:         'black'
};

/**
 * Enum for brightness level.
 * Note: if changed, will break compatibility with previously saved projects.
 * @readonly
 * @enum {string}
 */
const BrightnessLevelMenu = {
    LEV1:        '1',
    LEV2:        '2',
    LEV3:        '3',
    LEV4:        '4',
    LEV5:        '5',
    LEV6:        '6',
    ROLL_DICE:   'roll dice'
};

/**
 * Scratch 3.0 blocks to interact with a MatataBot peripheral.
 */
class Scratch3MatataBotBlocks {

    /**
     * @return {string} - the name of this extension.
     */
    static get EXTENSION_NAME () {
        return 'MatataBot';
    }

    /**
     * @return {string} - the ID of this extension.
     */
    static get EXTENSION_ID () {
        return 'matatabot';
    }

    /**
     * @return {array} - text and values for each step menu element
     */
    get MOTION_STEP_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.motionStepMenu.step1',
                    default: '1 step',
                    description: 'label for matatabot move 1 step'
                }),
                value: MotionStepMenu.STEP1
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionStepMenu.step2',
                    default: '2 steps',
                    description: 'label for matatabot move 2 steps'
                }),
                value: MotionStepMenu.STEP2
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionStepMenu.step3',
                    default: '3 steps',
                    description: 'label for matatabot move 3 steps'
                }),
                value: MotionStepMenu.STEP3
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionStepMenu.step4',
                    default: '4 steps',
                    description: 'label for matatabot move 4 steps'
                }),
                value: MotionStepMenu.STEP4
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionStepMenu.step5',
                    default: '5 steps',
                    description: 'label for matatabot move 5 steps'
                }),
                value: MotionStepMenu.STEP5
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionStepMenu.step6',
                    default: '6 steps',
                    description: 'label for matatabot move 6 steps'
                }),
                value: MotionStepMenu.STEP6
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionStepMenu.rollDice',
                    default: 'roll dice',
                    description: 'label for matatabot move roll dice steps'
                }),
                value: MotionStepMenu.ROLL_DICE
            }
        ];
    }

    /**
     * @return {array} - text and values for each angle menu element
     */
    get MOTION_ANGLE_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree30',
                    default: '30°',
                    description: 'label for matatabot turn 30 degree'
                }),
                value: MotionAngleMenu.ANGLE30
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree36',
                    default: '36°',
                    description: 'label for matatabot turn 36 degree'
                }),
                value: MotionAngleMenu.ANGLE36
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree45',
                    default: '45°',
                    description: 'label for matatabot turn 45 degree'
                }),
                value: MotionAngleMenu.ANGLE45
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree60',
                    default: '60°',
                    description: 'label for matatabot turn 60 degree'
                }),
                value: MotionAngleMenu.ANGLE60
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree72',
                    default: '72°',
                    description: 'label for matatabot turn 72 degree'
                }),
                value: MotionAngleMenu.ANGLE72
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree90',
                    default: '90°',
                    description: 'label for matatabot turn 90 degree'
                }),
                value: MotionAngleMenu.ANGLE90
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree108',
                    default: '108°',
                    description: 'label for matatabot turn 108 degree'
                }),
                value: MotionAngleMenu.ANGLE108
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree120',
                    default: '120°',
                    description: 'label for matatabot turn 120 degree'
                }),
                value: MotionAngleMenu.ANGLE120
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree135',
                    default: '135°',
                    description: 'label for matatabot turn 135 degree'
                }),
                value: MotionAngleMenu.ANGLE135
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree144',
                    default: '144°',
                    description: 'label for matatabot turn 144 degree'
                }),
                value: MotionAngleMenu.ANGLE144
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree150',
                    default: '150°',
                    description: 'label for matatabot turn 150 degree'
                }),
                value: MotionAngleMenu.ANGLE150
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionAngleMenu.degree180',
                    default: '180°',
                    description: 'label for matatabot turn 180 degree'
                }),
                value: MotionAngleMenu.ANGLE180
            }
        ];
    }

    /**
     * @return {array} - text and values for each trend menu element
     */
    get MOTION_TREND_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.motionTrendMenu.forward',
                    default: 'forward',
                    description: 'label for matatabot trend forward'
                }),
                value: MotionTrendMenu.FORWARD
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionTrendMenu.backward',
                    default: 'backward',
                    description: 'label for matatabot trend backward'
                }),
                value: MotionTrendMenu.BACKWARD
            }
        ];
    }

    /**
     * @return {array} - text and values for each gear menu element
     */
    get MOTION_GEAR_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.motionGearMenu.gear1',
                    default: 'gear 1',
                    description: 'label for matatabot gear 1'
                }),
                value: MotionGearMenu.GEAR1
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionGearMenu.gear2',
                    default: 'gear 2',
                    description: 'label for matatabot gear 2'
                }),
                value: MotionGearMenu.GEAR2
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionGearMenu.gear3',
                    default: 'gear 3',
                    description: 'label for matatabot gear 3'
                }),
                value: MotionGearMenu.GEAR3
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionGearMenu.gear4',
                    default: 'gear 4',
                    description: 'label for matatabot gear 4'
                }),
                value: MotionGearMenu.GEAR4
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionGearMenu.gear5',
                    default: 'gear 5',
                    description: 'label for matatabot gear 5'
                }),
                value: MotionGearMenu.GEAR5
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionGearMenu.gear6',
                    default: 'gear 6',
                    description: 'label for matatabot gear 6'
                }),
                value: MotionGearMenu.GEAR6
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionGearMenu.stop',
                    default: 'stop',
                    description: 'label for matatabot gear stop'
                }),
                value: MotionGearMenu.STOP
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionGearMenu.unchanged',
                    default: 'unchanged',
                    description: 'label for matatabot gear unchanged'
                }),
                value: MotionGearMenu.NULL
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionGearMenu.rollDice',
                    default: 'roll dice',
                    description: 'label for matatabot gear roll dice'
                }),
                value: MotionGearMenu.ROLL_DICE
            }
        ];
    }

    /**
     * @return {array} - text and values for each wheel menu element
     */
    get MOTION_WHEEL_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.motionWheelMenu.left',
                    default: 'left wheel',
                    description: 'label for matatabot left wheel'
                }),
                value: MotionWheelMenu.LEFT
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionWheelMenu.right',
                    default: 'right wheel',
                    description: 'label for matatabot right wheel'
                }),
                value: MotionWheelMenu.RIGHT
            },
            {
                text: formatMessage({
                    id: 'matatabot.motionWheelMenu.both',
                    default: 'both wheels',
                    description: 'label for matatabot both wheels'
                }),
                value: MotionWheelMenu.BOTH
            }
        ];
    }

    /**
     * @return {array} - text and values for each dance element
     */
    get DANCE_INDEX_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.danceIndexMenu.num1',
                    default: 'the 1st',
                    description: 'label for matatabot dance 1'
                }),
                value: DanceIndexMenu.NUM1
            },
            {
                text: formatMessage({
                    id: 'matatabot.danceIndexMenu.num2',
                    default: 'the 2nd',
                    description: 'label for matatabot dance 2'
                }),
                value: DanceIndexMenu.NUM2
            },
            {
                text: formatMessage({
                    id: 'matatabot.danceIndexMenu.num3',
                    default: 'the 3rd',
                    description: 'label for matatabot dance 3'
                }),
                value: DanceIndexMenu.NUM3
            },
            {
                text: formatMessage({
                    id: 'matatabot.danceIndexMenu.num4',
                    default: 'the 4th',
                    description: 'label for matatabot dance 4'
                }),
                value: DanceIndexMenu.NUM4
            },
            {
                text: formatMessage({
                    id: 'matatabot.danceIndexMenu.num5',
                    default: 'the 5th',
                    description: 'label for matatabot dance 5'
                }),
                value: DanceIndexMenu.NUM5
            },
            {
                text: formatMessage({
                    id: 'matatabot.danceIndexMenu.num6',
                    default: 'the 6th',
                    description: 'label for matatabot dance 6'
                }),
                value: DanceIndexMenu.NUM6
            },
            {
                text: formatMessage({
                    id: 'matatabot.danceIndexMenu.rollDice',
                    default: 'roll dice',
                    description: 'label for matatabot dance roll dice'
                }),
                value: DanceIndexMenu.ROLL_DICE
            }
        ];
    }

    /**
     * @return {array} - text and values for each action element
     */
    get ACTION_INDEX_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.actionIndexMenu.num1',
                    default: 'the 1st',
                    description: 'label for matatabot action 1'
                }),
                value: ActionIndexMenu.NUM1
            },
            {
                text: formatMessage({
                    id: 'matatabot.actionIndexMenu.num2',
                    default: 'the 2nd',
                    description: 'label for matatabot action 2'
                }),
                value: ActionIndexMenu.NUM2
            },
            {
                text: formatMessage({
                    id: 'matatabot.actionIndexMenu.num3',
                    default: 'the 3rd',
                    description: 'label for matatabot action 3'
                }),
                value: ActionIndexMenu.NUM3
            },
            {
                text: formatMessage({
                    id: 'matatabot.actionIndexMenu.num4',
                    default: 'the 4th',
                    description: 'label for matatabot action 4'
                }),
                value: ActionIndexMenu.NUM4
            },
            {
                text: formatMessage({
                    id: 'matatabot.actionIndexMenu.num5',
                    default: 'the 5th',
                    description: 'label for matatabot action 5'
                }),
                value: ActionIndexMenu.NUM5
            },
            {
                text: formatMessage({
                    id: 'matatabot.actionIndexMenu.num6',
                    default: 'the 6th',
                    description: 'label for matatabot action 6'
                }),
                value: ActionIndexMenu.NUM6
            },
            {
                text: formatMessage({
                    id: 'matatabot.actionIndexMenu.rollDice',
                    default: 'roll dice',
                    description: 'label for matatabot action roll dice'
                }),
                value: ActionIndexMenu.ROLL_DICE
            }
        ];
    }

    /**
     * @return {array} - text and values for each beat menu element
     */
    get SOUND_BEAT_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.soundBeat.oneFourth',
                    default: '1/4',
                    description: 'label for matatabot beats oneFourth'
                }),
                value: SoundBeatMenu.ONE_FOURTH
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundBeat.twoFourth',
                    default: '2/4',
                    description: 'label for matatabot beats twoFourth'
                }),
                value: SoundBeatMenu.TWO_FOURTH
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundBeat.threeFourth',
                    default: '3/4',
                    description: 'label for matatabot beats threeFourth'
                }),
                value: SoundBeatMenu.THREE_FOURTH
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundBeat.fourFourth',
                    default: '4/4',
                    description: 'label for matatabot beats fourFourth'
                }),
                value: SoundBeatMenu.FOUR_FOURTH
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundBeat.fiveFourth',
                    default: '5/4',
                    description: 'label for matatabot beats fiveFourth'
                }),
                value: SoundBeatMenu.FIVE_FOURTH
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundBeat.sixFourth',
                    default: '6/4',
                    description: 'label for matatabot beats sixFourth'
                }),
                value: SoundBeatMenu.SIX_FOURTH
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundBeat.rollDice',
                    default: 'roll dice',
                    description: 'label for matatabot beats roll dice'
                }),
                value: SoundBeatMenu.ROLL_DICE
            }
        ];
    }

    /**
     * @return {array} - text and values for each tone menu element
     */
    get SOUND_TONE_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.soundTone.do',
                    default: 'do',
                    description: 'label for matatabot tone do'
                }),
                value: SoundToneMenu.DO
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundTone.re',
                    default: 're',
                    description: 'label for matatabot tone re'
                }),
                value: SoundToneMenu.RE
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundTone.mi',
                    default: 'mi',
                    description: 'label for matatabot tone mi'
                }),
                value: SoundToneMenu.MI
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundTone.fa',
                    default: 'fa',
                    description: 'label for matatabot tone fa'
                }),
                value: SoundToneMenu.FA
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundTone.so',
                    default: 'so',
                    description: 'label for matatabot tone so'
                }),
                value: SoundToneMenu.SO
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundTone.la',
                    default: 'la',
                    description: 'label for matatabot beat la'
                }),
                value: SoundToneMenu.LA
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundTone.ti',
                    default: 'ti',
                    description: 'label for matatabot beat ti'
                }),
                value: SoundToneMenu.TI
            }
        ];
    }

    /**
     * @return {array} - text and values for each melody menu element
     */
    get SOUND_MELODY_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.soundMelody.num1',
                    default: 'the 1st',
                    description: 'label for matatabot melody1'
                }),
                value: SoundMelodyMenu.NUM1
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundMelody.num2',
                    default: 'the 2nd',
                    description: 'label for matatabot melody2'
                }),
                value: SoundMelodyMenu.NUM2
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundMelody.num3',
                    default: 'the 3rd',
                    description: 'label for matatabot melody3'
                }),
                value: SoundMelodyMenu.NUM3
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundMelody.num4',
                    default: 'the 4th',
                    description: 'label for matatabot melody4'
                }),
                value: SoundMelodyMenu.NUM4
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundMelody.num5',
                    default: 'the 5th',
                    description: 'label for matatabot melody5'
                }),
                value: SoundMelodyMenu.NUM5
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundMelody.num6',
                    default: 'the 6th',
                    description: 'label for matatabot melody6'
                }),
                value: SoundMelodyMenu.NUM6
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundMelody.num7',
                    default: 'the 7th',
                    description: 'label for matatabot melody7'
                }),
                value: SoundMelodyMenu.NUM7
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundMelody.num8',
                    default: 'the 8th',
                    description: 'label for matatabot melody8'
                }),
                value: SoundMelodyMenu.NUM8
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundMelody.num9',
                    default: 'the 9th',
                    description: 'label for matatabot melody9'
                }),
                value: SoundMelodyMenu.NUM9
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundMelody.num10',
                    default: 'the 10th',
                    description: 'label for matatabot melody10'
                }),
                value: SoundMelodyMenu.NUM10
            }
        ];
    }

    /**
     * @return {array} - text and values for each song menu element
     */
    get SOUND_SONG_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.soundSong.num1',
                    default: 'the 1st',
                    description: 'label for matatabot song1'
                }),
                value: SoundSongMenu.NUM1
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundSong.num2',
                    default: 'the 2nd',
                    description: 'label for matatabot song2'
                }),
                value: SoundSongMenu.NUM2
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundSong.num3',
                    default: 'the 3rd',
                    description: 'label for matatabot song3'
                }),
                value: SoundSongMenu.NUM3
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundSong.num4',
                    default: 'the 4th',
                    description: 'label for matatabot song4'
                }),
                value: SoundSongMenu.NUM4
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundSong.num5',
                    default: 'the 5th',
                    description: 'label for matatabot song5'
                }),
                value: SoundSongMenu.NUM5
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundSong.num6',
                    default: 'the 6th',
                    description: 'label for matatabot song6'
                }),
                value: SoundSongMenu.NUM6
            },
            {
                text: formatMessage({
                    id: 'matatabot.soundSong.rollDice',
                    default: 'roll dice',
                    description: 'label for matatabot song roll dice'
                }),
                value: SoundSongMenu.ROLL_DICE
            }
        ];
    }

    /**
     * @return {array} - text and values for each side menu element
     */
    get LOOKS_SIDE_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.looksSideMenu.left',
                    default: 'left',
                    description: 'label for matatabot side left'
                }),
                value: LooksSideMenu.LEFT
            },
            {
                text: formatMessage({
                    id: 'matatabot.looksSideMenu.right',
                    default: 'right',
                    description: 'label for matatabot side right'
                }),
                value: LooksSideMenu.RIGHT
            },
            {
                text: formatMessage({
                    id: 'matatabot.looksSideMenu.all',
                    default: 'all',
                    description: 'label for matatabot side all'
                }),
                value: LooksSideMenu.ALL
            }
        ];
    }

    /**
     * @return {array} - text and values for each color menu element
     */
    get COLOR_TYPE_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.colorTypeMenu.white',
                    default: 'white',
                    description: 'The color is white'
                }),
                value: ColorTypeMenu.WHITE
            },
            {
                text: formatMessage({
                    id: 'matatabot.colorTypeMenu.red',
                    default: 'red',
                    description: 'The color is red'
                }),
                value: ColorTypeMenu.RED
            },
            {
                text: formatMessage({
                    id: 'matatabot.colorTypeMenu.yellow',
                    default: 'yellow',
                    description: 'The color is yellow'
                }),
                value: ColorTypeMenu.YELLOW
            },
            {
                text: formatMessage({
                    id: 'matatabot.colorTypeMenu.green',
                    default: 'green',
                    description: 'The color is green'
                }),
                value: ColorTypeMenu.GREEN
            },
            {
                text: formatMessage({
                    id: 'matatabot.colorTypeMenu.blue',
                    default: 'blue',
                    description: 'The color is blue'
                }),
                value: ColorTypeMenu.BLUE
            },
            {
                text: formatMessage({
                    id: 'matatabot.colorTypeMenu.purple',
                    default: 'purple',
                    description: 'The color is purple'
                }),
                value: ColorTypeMenu.PURPLE
            },
            {
                text: formatMessage({
                    id: 'matatabot.colorTypeMenu.black',
                    default: 'black',
                    description: 'The color is black'
                }),
                value: ColorTypeMenu.BLACK
            }
        ];
    }

    /**
     * @return {array} - text and values for each brightness level menu element
     */
    get BRIGHTNESS_LEVEL_MENU () {
        return [
            {
                text: formatMessage({
                    id: 'matatabot.brightnessLevelMenu.lev1',
                    default: '1',
                    description: 'led brightness level 1'
                }),
                value: BrightnessLevelMenu.LEV1
            },
            {
                text: formatMessage({
                    id: 'matatabot.brightnessLevelMenu.lev2',
                    default: '2',
                    description: 'led brightness level 2'
                }),
                value: BrightnessLevelMenu.LEV2
            },
            {
                text: formatMessage({
                    id: 'matatabot.brightnessLevelMenu.lev3',
                    default: '3',
                    description: 'led brightness level 3'
                }),
                value: BrightnessLevelMenu.LEV3
            },
            {
                text: formatMessage({
                    id: 'matatabot.brightnessLevelMenu.lev4',
                    default: '4',
                    description: 'led brightness level 4'
                }),
                value: BrightnessLevelMenu.LEV4
            },
            {
                text: formatMessage({
                    id: 'matatabot.brightnessLevelMenu.lev5',
                    default: '5',
                    description: 'led brightness level 5'
                }),
                value: BrightnessLevelMenu.LEV5
            },
            {
                text: formatMessage({
                    id: 'matatabot.brightnessLevelMenu.lev6',
                    default: '6',
                    description: 'led brightness level 6'
                }),
                value: BrightnessLevelMenu.LEV6
            },
            {
                text: formatMessage({
                    id: 'matatabot.brightnessLevelMenu.rollDice',
                    default: 'roll dice',
                    description: 'roll dice led brightness'
                }),
                value: BrightnessLevelMenu.ROLL_DICE
            }
        ];
    }

    /**
     * Construct a set of MatataBot blocks.
     * @param {Runtime} runtime - the Scratch 3.0 runtime.
     */
    constructor (runtime) {
        /**
         * The Scratch 3.0 runtime.
         * @type {Runtime}
         */
        this.runtime = runtime;

        // Create a new MatataBot peripheral instance
        this._peripheral = new MatataBot(this.runtime, Scratch3MatataBotBlocks.EXTENSION_ID);
        window.mext = this._peripheral;
    }

    /**
     * @returns {object} metadata for this extension and its blocks.
     */
    getInfo () {
        return {
            id: Scratch3MatataBotBlocks.EXTENSION_ID,
            name: Scratch3MatataBotBlocks.EXTENSION_NAME,
            blockIconURI: blockIconURI,
            showStatusButton: true,
            blocks: [
                {
                    opcode: 'motionForwardStep',
                    text: formatMessage({
                        id: 'matatabot.motionForwardStep',
                        default: 'move [STEP] forward',
                        description: 'Move forward according to the set number of steps'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        STEP: {
                            type: ArgumentType.STRING,
                            menu: 'motionStep',
                            defaultValue: MotionStepMenu.STEP1
                        }
                    }
                },
                {
                    opcode: 'motionBackwardStep',
                    text: formatMessage({
                        id: 'matatabot.motionBackwardStep',
                        default: 'move [STEP] backward',
                        description: 'Move backward according to the set number of steps'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        STEP: {
                            type: ArgumentType.STRING,
                            menu: 'motionStep',
                            defaultValue: MotionStepMenu.STEP1
                        }
                    }
                },
                {
                    opcode: 'motionTurnLeftAngle',
                    text: formatMessage({
                        id: 'matatabot.motionTurnLeftAngle',
                        default: 'turn [ANGLE] left',
                        description: 'Turn left according to the set number of angle'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        ANGLE: {
                            type: ArgumentType.STRING,
                            menu: 'motionAngle',
                            defaultValue: MotionAngleMenu.ANGLE90
                        }
                    }
                },
                {
                    opcode: 'motionTurnRightAngle',
                    text: formatMessage({
                        id: 'matatabot.motionTurnRightAngle',
                        default: 'turn [ANGLE] right',
                        description: 'Turn right according to the set number of angle'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        ANGLE: {
                            type: ArgumentType.STRING,
                            menu: 'motionAngle',
                            defaultValue: MotionAngleMenu.ANGLE90
                        }
                    }
                },
                {
                    opcode: 'motionMoving',
                    text: formatMessage({
                        id: 'matatabot.motionMoving',
                        default: 'move [POS] cm',
                        description: 'Moving'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        POS: {
                            type: ArgumentType.NUMBER,
                            defaultValue: 10
                        }
                    }
                },
                {
                    opcode: 'motionWhirl',
                    text: formatMessage({
                        id: 'matatabot.motionWhirl',
                        default: 'rotate [ANGLE] degrees',
                        description: 'Whirl'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        ANGLE: {
                            type: ArgumentType.NUMBER,
                            defaultValue: 90
                        }
                    }
                },
                {
                    opcode: 'motionWheelPower',
                    text: formatMessage({
                        id: 'matatabot.motionWheelPower',
                        default: 'set left wheel [LEFT_DIR] speed [LEFT_GEAR], set right wheel [RIGHT_DIR] speed [RIGHT_GEAR]',
                        description: 'left motor gear move and right motor gear move'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        LEFT_DIR: {
                            type: ArgumentType.STRING,
                            menu: 'motionTrend',
                            defaultValue: MotionTrendMenu.FORWARD
                        },
                        LEFT_GEAR: {
                            type: ArgumentType.STRING,
                            menu: 'motionGear',
                            defaultValue: MotionGearMenu.GEAR1
                        },
                        RIGHT_DIR: {
                            type: ArgumentType.STRING,
                            menu: 'motionTrend',
                            defaultValue: MotionTrendMenu.FORWARD
                        },
                        RIGHT_GEAR: {
                            type: ArgumentType.STRING,
                            menu: 'motionGear',
                            defaultValue: MotionGearMenu.GEAR1
                        }
                    }
                },
                {
                    opcode: 'motionWheelSpeed',
                    text: formatMessage({
                        id: 'matatabot.motionWheelSpeed',
                        default: 'set left motor speed [LEFT] cm/s, set right motor speed [RIGHT] cm/s',
                        description: 'left motor speed and right motor speed'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        LEFT: {
                            type: ArgumentType.NUMBER,
                            defaultValue: 10
                        },
                        RIGHT: {
                            type: ArgumentType.NUMBER,
                            defaultValue: 10
                        }
                    }
                },
                {
                    opcode: 'motionStopMoving',
                    text: formatMessage({
                        id: 'matatabot.motionStopMoving',
                        default: '[WHEEL] stop moving',
                        description: 'Stop moving'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        WHEEL: {
                            type: ArgumentType.STRING,
                            menu: 'motionWheel',
                            defaultValue: MotionWheelMenu.BOTH
                        }
                    }
                },
                {
                    opcode: 'doDance',
                    text: formatMessage({
                        id: 'matatabot.doDance',
                        default: 'perform [DANCE_INDEX] dance',
                        description: 'Dance'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        DANCE_INDEX: {
                            type: ArgumentType.STRING,
                            menu: 'danceIndex',
                            defaultValue: DanceIndexMenu.NUM1
                        }
                    }
                },
                {
                    opcode: 'doAction',
                    text: formatMessage({
                        id: 'matatabot.doAction',
                        default: 'perform [ACTION_INDEX] action',
                        description: 'Action'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        ACTION_INDEX: {
                            type: ArgumentType.STRING,
                            menu: 'actionIndex',
                            defaultValue: ActionIndexMenu.NUM1
                        }
                    }
                },
                {
                    opcode: 'soundAlto',
                    text: formatMessage({
                        id: 'matatabot.soundAlto',
                        default: 'play alto [TONE] for [BEAT] beats',
                        description: 'play a alto sound'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        BEAT: {
                            type: ArgumentType.STRING,
                            menu: 'soundBeat',
                            defaultValue: SoundBeatMenu.quarter
                        },
                        TONE: {
                            type: ArgumentType.STRING,
                            menu: 'soundTone',
                            defaultValue: SoundBeatMenu.Do
                        }
                    }
                },
                {
                    opcode: 'soundTreble',
                    text: formatMessage({
                        id: 'matatabot.soundTreble',
                        default: 'play treble [TONE] for [BEAT] beats',
                        description: 'play a treble sound'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        BEAT: {
                            type: ArgumentType.STRING,
                            menu: 'soundBeat',
                            defaultValue: SoundBeatMenu.quarter
                        },
                        TONE: {
                            type: ArgumentType.STRING,
                            menu: 'soundTone',
                            defaultValue: SoundBeatMenu.Do
                        }
                    }
                },
                {
                    opcode: 'soundMelody',
                    text: formatMessage({
                        id: 'matatabot.soundMelody',
                        default: 'play [MELODY] melody',
                        description: 'play a melody'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        MELODY: {
                            type: ArgumentType.STRING,
                            menu: 'soundMelody',
                            defaultValue: SoundMelodyMenu.NUM1
                        }
                    }
                },
                {
                    opcode: 'soundSong',
                    text: formatMessage({
                        id: 'matatabot.soundSong',
                        default: 'play [SONG] song',
                        description: 'play a sound'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        SONG: {
                            type: ArgumentType.STRING,
                            menu: 'soundSong',
                            defaultValue: SoundSongMenu.NUM1
                        }
                    }
                },
                {
                    opcode: 'eyeLedSingleSet1',
                    text: formatMessage({
                        id: 'matatabot.eyeLedSingleSet1',
                        default: 'set [SIDE] eye(s) to [COLOR_TYPE] and brightness is level [BRIGHTNESS_LEVEL]',
                        description: 'set eye LED function 1'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        SIDE: {
                            type: ArgumentType.STRING,
                            menu: 'looksSide',
                            defaultValue: LooksSideMenu.ALL
                        },
                        COLOR_TYPE: {
                            type: ArgumentType.STRING,
                            menu: 'colorType',
                            defaultValue: ColorTypeMenu.WHITE
                        },
                        BRIGHTNESS_LEVEL: {
                            type: ArgumentType.STRING,
                            menu: 'brightnessLevel',
                            defaultValue: BrightnessLevelMenu.LEV1
                        }
                    }
                },
                {
                    opcode: 'eyeLedSingleSet2',
                    text: formatMessage({
                        id: 'matatabot.eyeLedSingleSet2',
                        default: 'set [SIDE] eye(s) to color [COLOR]',
                        description: 'set eye LED function 2'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        SIDE: {
                            type: ArgumentType.STRING,
                            menu: 'looksSide',
                            defaultValue: LooksSideMenu.ALL
                        },
                        COLOR: {
                            type: ArgumentType.COLOR
                        }
                    }
                }, 
                {
                    opcode: 'eyeLedSingleSet3',
                    text: formatMessage({
                        id: 'matatabot.eyeLedSingleSet3',
                        default: 'set eye [LED_INDEX] red [RED_VALUE] green [GREEN_VALUE] blue [BLUE_VALUE]',
                        description: 'set single LED function 3'
                    }),
                    blockType: BlockType.COMMAND,
                    arguments: {
                        LED_INDEX: {
                            type: ArgumentType.NUMBER,
                            defaultValue: 0
                        },
                        RED_VALUE: {
                            type: ArgumentType.NUMBER,
                            defaultValue: 50
                        },
                        GREEN_VALUE: {
                            type: ArgumentType.NUMBER,
                            defaultValue: 50
                        },
                        BLUE_VALUE: {
                            type: ArgumentType.NUMBER,
                            defaultValue: 50
                        }
                    }
                },
                {
                    opcode: 'eyeLedAllOff',
                    text: formatMessage({
                        id: 'matatabot.eyeLedAllOff',
                        default: 'turn all eyes off',
                        description: 'turn all eyes lights off'
                    }),
                    blockType: BlockType.COMMAND
                }
            ],
            menus: {
                motionStep: {
                    acceptReporters: true,
                    items: this.MOTION_STEP_MENU
                },
                motionAngle: {
                    acceptReporters: true,
                    items: this.MOTION_ANGLE_MENU
                },
                motionTrend: {
                    acceptReporters: true,
                    items: this.MOTION_TREND_MENU
                },
                motionGear: {
                    acceptReporters: true,
                    items: this.MOTION_GEAR_MENU
                },
                motionWheel: {
                    acceptReporters: true,
                    items: this.MOTION_WHEEL_MENU
                },
                danceIndex: {
                    acceptReporters: true,
                    items: this.DANCE_INDEX_MENU
                },
                actionIndex: {
                    acceptReporters: true,
                    items: this.ACTION_INDEX_MENU
                },
                soundBeat: {
                    acceptReporters: true,
                    items: this.SOUND_BEAT_MENU
                },
                soundTone: {
                    acceptReporters: true,
                    items: this.SOUND_TONE_MENU
                },
                soundMelody: {
                    acceptReporters: true,
                    items: this.SOUND_MELODY_MENU
                },
                soundSong: {
                    acceptReporters: true,
                    items: this.SOUND_SONG_MENU
                },
                looksSide: {
                    acceptReporters: true,
                    items: this.LOOKS_SIDE_MENU
                },
                colorType: {
                    acceptReporters: true,
                    items: this.COLOR_TYPE_MENU
                },
                brightnessLevel: {
                    acceptReporters: true,
                    items: this.BRIGHTNESS_LEVEL_MENU
                }
            }
        };
    }

    rollDice () {
        return Math.floor(Math.random() * 6) + 1;
    }

    motionForwardStep (args) {
        const motionForwardStepData = new Array();
        let movePos = 1;
        if (args.STEP === MotionStepMenu.STEP1) {
            movePos = 1;
        } else if (args.STEP === MotionStepMenu.STEP2) {
            movePos = 2;
        } else if (args.STEP === MotionStepMenu.STEP3) {
            movePos = 3;
        } else if (args.STEP === MotionStepMenu.STEP4) {
            movePos = 4;
        } else if (args.STEP === MotionStepMenu.STEP5) {
            movePos = 5;
        } else if (args.STEP === MotionStepMenu.STEP6) {
            movePos = 6;
        } else if (args.STEP === MotionStepMenu.ROLL_DICE) {
            movePos = this.rollDice();
        }
        let movePosCount = movePos;

        motionForwardStepData.push(BLECommand.CMD_MOVE_POS);
        motionForwardStepData.push(MovePosCommand.MOVE_FORWARD);

        // 100mm move pos
        motionForwardStepData.push(0x00);
        motionForwardStepData.push(0x64);

        this._peripheral.commandSyncFlag.motionForwardStepFlag = true;
        this._peripheral.send(this._peripheral.packCommand(motionForwardStepData));
        movePosCount = movePosCount - 1;
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 2000 * movePos) {
                    // eslint-disable-next-line no-console
                    console.log('motionForwardStep timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.motionForwardStepFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.motionForwardStepFlag === false) {
                    if (movePosCount > 0) {
                        this._peripheral.send(this._peripheral.packCommand(motionForwardStepData));
                        movePosCount = movePosCount - 1;
                        this._peripheral.commandSyncFlag.motionForwardStepFlag = true;
                    } else {
                        clearInterval(interval);
                        resolve();
                    }
                }
                count += 10;
            }, 10);
        });
    }

    motionBackwardStep (args) {
        const motionBackwardStepData = new Array();
        let movePos = 1;
        if (args.STEP === MotionStepMenu.STEP1) {
            movePos = 1;
        } else if (args.STEP === MotionStepMenu.STEP2) {
            movePos = 2;
        } else if (args.STEP === MotionStepMenu.STEP3) {
            movePos = 3;
        } else if (args.STEP === MotionStepMenu.STEP4) {
            movePos = 4;
        } else if (args.STEP === MotionStepMenu.STEP5) {
            movePos = 5;
        } else if (args.STEP === MotionStepMenu.STEP6) {
            movePos = 6;
        } else if (args.STEP === MotionStepMenu.ROLL_DICE) {
            movePos = this.rollDice();
        }
        let movePosCount = movePos;

        motionBackwardStepData.push(BLECommand.CMD_MOVE_POS);
        motionBackwardStepData.push(MovePosCommand.MOVE_BACKWARD);

        // 100mm move pos
        motionBackwardStepData.push(0x00);
        motionBackwardStepData.push(0x64);

        this._peripheral.commandSyncFlag.motionForwardStepFlag = true;
        this._peripheral.send(this._peripheral.packCommand(motionBackwardStepData));
        movePosCount = movePosCount - 1;
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 2000 * movePos) {
                    console.log('motionBackwardStep timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.motionForwardStepFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.motionForwardStepFlag === false) {
                    if (movePosCount > 0) {
                        this._peripheral.send(this._peripheral.packCommand(motionBackwardStepData));
                        movePosCount = movePosCount - 1;
                        this._peripheral.commandSyncFlag.motionForwardStepFlag = true;
                    } else {
                        clearInterval(interval);
                        resolve();
                    }
                }
                count += 10;
            }, 10);
        });
    }

    motionTurnLeftAngle (args) {
        const motionTurnLeftAngleData = new Array();
        let moveAngle = 90;
        if (args.ANGLE === MotionAngleMenu.ANGLE30) {
            moveAngle = 30;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE36) {
            moveAngle = 36;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE45) {
            moveAngle = 45;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE60) {
            moveAngle = 60;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE72) {
            moveAngle = 72;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE90) {
            moveAngle = 90;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE108) {
            moveAngle = 108;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE120) {
            moveAngle = 120;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE135) {
            moveAngle = 135;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE144) {
            moveAngle = 144;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE150) {
            moveAngle = 150;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE180) {
            moveAngle = 180;
        }

        motionTurnLeftAngleData.push(BLECommand.CMD_MOVE_POS);
        motionTurnLeftAngleData.push(MovePosCommand.MOVE_LEFT);
        motionTurnLeftAngleData.push(0x00);
        motionTurnLeftAngleData.push(moveAngle);

        this._peripheral.commandSyncFlag.motionTurnLeftAngleFlag = true;
        this._peripheral.send(this._peripheral.packCommand(motionTurnLeftAngleData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 20 * moveAngle) {
                    console.log('motionTurnLeftAngle timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.motionTurnLeftAngleFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.motionTurnLeftAngleFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    motionTurnRightAngle (args) {
        const motionTurnRightAngleData = new Array();
        let moveAngle = 90;
        if (args.ANGLE === MotionAngleMenu.ANGLE30) {
            moveAngle = 30;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE36) {
            moveAngle = 36;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE45) {
            moveAngle = 45;
        } else if (args.ANGLE === MotionAngleMenu.ANGLEle60) {
            moveAngle = 60;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE72) {
            moveAngle = 72;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE90) {
            moveAngle = 90;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE108) {
            moveAngle = 108;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE120) {
            moveAngle = 120;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE135) {
            moveAngle = 135;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE144) {
            moveAngle = 144;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE150) {
            moveAngle = 150;
        } else if (args.ANGLE === MotionAngleMenu.ANGLE180) {
            moveAngle = 180;
        }

        motionTurnRightAngleData.push(BLECommand.CMD_MOVE_POS);
        motionTurnRightAngleData.push(MovePosCommand.MOVE_RIGHT);
        motionTurnRightAngleData.push(0x00);
        motionTurnRightAngleData.push(moveAngle);

        this._peripheral.commandSyncFlag.motionTurnRightAngleFlag = true;
        this._peripheral.send(this._peripheral.packCommand(motionTurnRightAngleData));

        const timeout = MathUtil.clamp(15 * Math.abs(moveAngle), 1000, 2700);
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > timeout) {
                    console.log('motionTurnRightAngle timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.motionTurnRightAngleFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.motionTurnRightAngleFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    motionMoving (args) {
        const motionMovingData = new Array();
        const movePos = MathUtil.clamp(args.POS * 10, -1000, 1000);
        const timeout = MathUtil.clamp(15 * Math.abs(movePos), 1000, 15000);
        motionMovingData.push(BLECommand.CMD_MOVE_POS);
        if (movePos >= 0) {
            motionMovingData.push(MovePosCommand.MOVE_FORWARD);
        } else {
            motionMovingData.push(MovePosCommand.MOVE_BACKWARD);
        }
        motionMovingData.push((Math.abs(movePos) & 0xff00) >> 8);
        motionMovingData.push(Math.abs(movePos) & 0xff);

        this._peripheral.commandSyncFlag.motionMovingFlag = true;
        this._peripheral.send(this._peripheral.packCommand(motionMovingData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > timeout) {
                    console.log('motionMoving timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.motionMovingFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.motionMovingFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    motionWhirl (args) {
        const motionWhirlData = new Array();
        const moveAngle = MathUtil.clamp(args.ANGLE, -360, 360);
        const timeout = MathUtil.clamp(15 * Math.abs(moveAngle), 1000, 4800);
        motionWhirlData.push(BLECommand.CMD_MOVE_POS);
        if (moveAngle >= 0) {
            motionWhirlData.push(MovePosCommand.MOVE_RIGHT);
        } else {
            motionWhirlData.push(MovePosCommand.MOVE_LEFT);
        }
        motionWhirlData.push((Math.abs(moveAngle) & 0xff00) >> 8);
        motionWhirlData.push(Math.abs(moveAngle) & 0xff);

        this._peripheral.commandSyncFlag.motionWhirlFlag = true;
        this._peripheral.send(this._peripheral.packCommand(motionWhirlData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > timeout) {
                    console.log('motionWhirl timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.motionWhirlFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.motionWhirlFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    motionWheelPower (args) {
        const motionWheelPowerData = new Array();
        let left_dir = 0x01;
        let left_speed = 0x01; // 70
        let right_dir = 0x01;
        let right_speed = 0x01; // 70
        let speed_left = 0;
        let speed_right = 0;
        if (args.LEFT_DIR === MotionTrendMenu.FORWARD) {
            left_dir = 0x01;
        } else if (args.LEFT_DIR === MotionTrendMenu.BACKWARD) {
            left_dir = 0x02;
        }
        if (args.LEFT_GEAR === MotionGearMenu.GEAR1) {
            left_speed = 0x01;
        } else if (args.LEFT_GEAR === MotionGearMenu.GEAR2) {
            left_speed = 0x02;
        } else if (args.LEFT_GEAR === MotionGearMenu.GEAR3) {
            left_speed = 0x03;
        } else if (args.LEFT_GEAR === MotionGearMenu.GEAR4) {
            left_speed = 0x04;
        } else if (args.LEFT_GEAR === MotionGearMenu.GEAR5) {
            left_speed = 0x05;
        } else if (args.LEFT_GEAR === MotionGearMenu.GEAR6) {
            left_speed = 0x06;
        } else if (args.LEFT_GEAR === MotionGearMenu.STOP) {
            left_speed = 0x00;
        } else if (args.LEFT_GEAR === MotionGearMenu.NULL) {
            left_speed = 0x08;
        } else if (args.LEFT_GEAR === MotionGearMenu.ROLL_DICE) {
            left_speed = this.rollDice();
        }

        if (args.RIGHT_DIR === MotionTrendMenu.FORWARD) {
            right_dir = 0x01;
        } else if (args.RIGHT_DIR === MotionTrendMenu.BACKWARD) {
            right_dir = 0x02;
        }

        if (args.RIGHT_GEAR === MotionGearMenu.GEAR1) {
            right_speed = 0x01;
        } else if (args.RIGHT_GEAR === MotionGearMenu.GEAR2) {
            right_speed = 0x02;
        } else if (args.RIGHT_GEAR === MotionGearMenu.GEAR3) {
            right_speed = 0x03;
        } else if (args.RIGHT_GEAR === MotionGearMenu.GEAR4) {
            right_speed = 0x04;
        } else if (args.RIGHT_GEAR === MotionGearMenu.GEAR5) {
            right_speed = 0x05;
        } else if (args.RIGHT_GEAR === MotionGearMenu.GEAR6) {
            right_speed = 0x06;
        } else if (args.RIGHT_GEAR === MotionGearMenu.STOP) {
            right_speed = 0x00;
        } else if (args.RIGHT_GEAR === MotionGearMenu.NULL) {
            right_speed = 0x08;
        } else if (args.RIGHT_GEAR === MotionGearMenu.ROLL_DICE) {
            right_speed = this.rollDice();
        }

        if (left_speed === 0x00) {
            speed_left = 0;
        } else if (left_speed < 0x08) {
            speed_left = 35 + (35 * left_speed);
        }

        if (right_speed === 0x00) {
            speed_right = 0;
        } else if (right_speed < 0x08) {
            speed_right = 35 + (35 * right_speed);
        }

        if ((left_speed === 0x08) && (right_speed !== 0x08)) {
            motionWheelPowerData.push(BLECommand.CMD_MOVE_SPEED);
            motionWheelPowerData.push(MoveSpeedCommand.MOTION_RIGHT);
            motionWheelPowerData.push(right_dir);
            motionWheelPowerData.push((speed_right & 0xff00) >> 8);
            motionWheelPowerData.push(speed_right & 0xff);
            this._peripheral.commandSyncFlag.motionWheelPowerFlag = true;
        } else if ((left_speed !== 0x08) && (right_speed === 0x08)) {
            motionWheelPowerData.push(BLECommand.CMD_MOVE_SPEED);
            motionWheelPowerData.push(MoveSpeedCommand.MOTION_LEFT);
            motionWheelPowerData.push(left_dir);
            motionWheelPowerData.push((speed_left & 0xff00) >> 8);
            motionWheelPowerData.push(speed_left & 0xff);
            this._peripheral.commandSyncFlag.motionWheelPowerFlag = true;
        } else if ((left_speed === 0x08) && (right_speed === 0x08)) {
            this._peripheral.commandSyncFlag.motionWheelPowerFlag = false;
        } else {
            motionWheelPowerData.push(BLECommand.CMD_MOVE_SPEED);
            motionWheelPowerData.push(MoveSpeedCommand.MOTION_BOTH);
            motionWheelPowerData.push(left_dir);
            motionWheelPowerData.push((speed_left & 0xff00) >> 8);
            motionWheelPowerData.push(speed_left & 0xff);
            motionWheelPowerData.push(right_dir);
            motionWheelPowerData.push((speed_right & 0xff00) >> 8);
            motionWheelPowerData.push(speed_right & 0xff);
            this._peripheral.commandSyncFlag.motionWheelPowerFlag = true;
        }
        this._peripheral.send(this._peripheral.packCommand(motionWheelPowerData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 1000) {
                    console.log('motionWheelPower timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.motionWheelPowerFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.motionWheelPowerFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    motionWheelSpeed (args) {
        const motionWheelSpeedData = new Array();
        const left_speed = Math.round(144.0 * (MathUtil.clamp(args.LEFT, -17, 17) / 9.7));
        const right_speed = Math.round(144.0 * (MathUtil.clamp(args.RIGHT, -17, 17) / 9.7));
        motionWheelSpeedData.push(BLECommand.CMD_MOVE_SPEED);
        motionWheelSpeedData.push(MoveSpeedCommand.MOTION_BOTH);
        if (left_speed >= 0) {
            motionWheelSpeedData.push(0x01);
        } else {
            motionWheelSpeedData.push(0x02);
        }
        motionWheelSpeedData.push((Math.abs(left_speed) & 0xff00) >> 8);
        motionWheelSpeedData.push(Math.abs(left_speed) & 0xff);
        if (right_speed >= 0) {
            motionWheelSpeedData.push(0x01);
        } else {
            motionWheelSpeedData.push(0x02);
        }
        motionWheelSpeedData.push((Math.abs(right_speed) & 0xff00) >> 8);
        motionWheelSpeedData.push(Math.abs(right_speed) & 0xff);

        this._peripheral.commandSyncFlag.motionWheelSpeedFlag = true;
        this._peripheral.send(this._peripheral.packCommand(motionWheelSpeedData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 1000) {
                    console.log('motionWheelSpeed timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.motionWheelSpeedFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.motionWheelSpeedFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    motionStopMoving (args) {
        const motionStopMovingData = new Array();
        let wheel_channel = 0x01;
        if (args.WHEEL === MotionWheelMenu.LEFT) {
            wheel_channel = 0x01;
        } else if (args.WHEEL === MotionWheelMenu.RIGHT) {
            wheel_channel = 0x02;
        } else if (args.WHEEL === MotionWheelMenu.BOTH) {
            wheel_channel = 0x03;
        }

        motionStopMovingData.push(BLECommand.CMD_MOVE_SPEED);
        if (wheel_channel === 0x01) {
            motionStopMovingData.push(MoveSpeedCommand.MOTION_LEFT);
            motionStopMovingData.push(0x01);
            motionStopMovingData.push(0x00);
            motionStopMovingData.push(0x00);
        } else if (wheel_channel === 0x02) {
            motionStopMovingData.push(MoveSpeedCommand.MOTION_RIGHT);
            motionStopMovingData.push(0x01);
            motionStopMovingData.push(0x00);
            motionStopMovingData.push(0x00);
        } else if (wheel_channel === 0x03) {
            motionStopMovingData.push(MoveSpeedCommand.MOTION_BOTH);
            motionStopMovingData.push(0x01);
            motionStopMovingData.push(0x00);
            motionStopMovingData.push(0x00);
            motionStopMovingData.push(0x01);
            motionStopMovingData.push(0x00);
            motionStopMovingData.push(0x00);
        }
        this._peripheral.commandSyncFlag.motionStopMovingFlag = true;
        this._peripheral.send(this._peripheral.packCommand(motionStopMovingData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 1000) {
                    console.log('motionStopMoving timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.motionStopMovingFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.motionStopMovingFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    doDance (args) {
        const doDanceData = new Array();
        let dance_index = 0x01;
        if (args.DANCE_INDEX === DanceIndexMenu.NUM1) {
            dance_index = 0x01;
        } else if (args.DANCE_INDEX === DanceIndexMenu.NUM2) {
            dance_index = 0x02;
        } else if (args.DANCE_INDEX === DanceIndexMenu.NUM3) {
            dance_index = 0x03;
        } else if (args.DANCE_INDEX === DanceIndexMenu.NUM4) {
            dance_index = 0x04;
        } else if (args.DANCE_INDEX === DanceIndexMenu.NUM5) {
            dance_index = 0x05;
        } else if (args.DANCE_INDEX === DanceIndexMenu.NUM6) {
            dance_index = 0x06;
        } else if (args.DANCE_INDEX === DanceIndexMenu.ROLL_DICE) {
            dance_index = this.rollDice();
        }

        doDanceData.push(BLECommand.CMD_DANCE);
        doDanceData.push(dance_index);

        this._peripheral.commandSyncFlag.doDanceFlag = true;
        this._peripheral.send(this._peripheral.packCommand(doDanceData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 1000) {
                    console.log('doDance timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.doDanceFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.doDanceFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    doAction (args) {
        const doActionData = new Array();
        let action_index = 0x01;
        if (args.ACTION_INDEX === ActionIndexMenu.NUM1) {
            action_index = 0x01;
        } else if (args.ACTION_INDEX === ActionIndexMenu.NUM2) {
            action_index = 0x02;
        } else if (args.ACTION_INDEX === ActionIndexMenu.NUM3) {
            action_index = 0x03;
        } else if (args.ACTION_INDEX === ActionIndexMenu.NUM4) {
            action_index = 0x04;
        } else if (args.ACTION_INDEX === ActionIndexMenu.NUM5) {
            action_index = 0x05;
        } else if (args.ACTION_INDEX === ActionIndexMenu.NUM6) {
            action_index = 0x06;
        } else if (args.ACTION_INDEX === ActionIndexMenu.ROLL_DICE) {
            action_index = this.rollDice();
        }

        doActionData.push(BLECommand.CMD_ACTION);
        doActionData.push(action_index);

        this._peripheral.commandSyncFlag.doActionFlag = true;
        this._peripheral.send(this._peripheral.packCommand(doActionData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 1000) {
                    console.log('doAction timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.doActionFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.doActionFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    soundAlto (args) {
        const soundAltoData = new Array();
        let tone_freq = noteFreq.C3;
        let beat_time = 0x01;

        if (args.TONE === SoundToneMenu.DO) {
            tone_freq = noteFreq.C3;
        } else if (args.TONE === SoundToneMenu.RE) {
            tone_freq = noteFreq.D3;
        } else if (args.TONE === SoundToneMenu.MI) {
            tone_freq = noteFreq.E3;
        } else if (args.TONE === SoundToneMenu.FA) {
            tone_freq = noteFreq.F3;
        } else if (args.TONE === SoundToneMenu.SO) {
            tone_freq = noteFreq.G3;
        } else if (args.TONE === SoundToneMenu.LA) {
            tone_freq = noteFreq.A3;
        } else if (args.TONE === SoundToneMenu.TI) {
            tone_freq = noteFreq.B3;
        }

        if (args.BEAT === SoundBeatMenu.ONE_FOURTH) {
            beat_time = 0x01;
        } else if (args.BEAT === SoundBeatMenu.TWO_FOURTH) {
            beat_time = 0x02;
        } else if (args.BEAT === SoundBeatMenu.THREE_FOURTH) {
            beat_time = 0x03;
        } else if (args.BEAT === SoundBeatMenu.FOUR_FOURTH) {
            beat_time = 0x04;
        } else if (args.BEAT === SoundBeatMenu.FIVE_FOURTH) {
            beat_time = 0x05;
        } else if (args.BEAT === SoundBeatMenu.SIX_FOURTH) {
            beat_time = 0x06;
        } else if (args.BEAT === SoundBeatMenu.ROLL_DICE) {
            beat_time = this.rollDice();
        }

        soundAltoData.push(BLECommand.CMD_PLAY_TONE);
        soundAltoData.push((tone_freq & 0xff00) >> 8);
        soundAltoData.push(tone_freq & 0x00ff);
        soundAltoData.push(((beat_time * 500) & 0xff00) >> 8);
        soundAltoData.push((beat_time * 500) & 0xff);

        this._peripheral.commandSyncFlag.soundAltoFlag = true;
        this._peripheral.send(this._peripheral.packCommand(soundAltoData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > ((beat_time * 500) + 100)) {
                    console.log('soundAlto timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.soundAltoFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.soundAltoFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    soundTreble (args) {
        const soundAltoData = new Array();
        let tone_freq = noteFreq.C4;
        let beat_time = 0x01;

        if (args.TONE === SoundToneMenu.DO) {
            tone_freq = noteFreq.C4;
        } else if (args.TONE === SoundToneMenu.RE) {
            tone_freq = noteFreq.D4;
        } else if (args.TONE === SoundToneMenu.MI) {
            tone_freq = noteFreq.E4;
        } else if (args.TONE === SoundToneMenu.FA) {
            tone_freq = noteFreq.F4;
        } else if (args.TONE === SoundToneMenu.SO) {
            tone_freq = noteFreq.G4;
        } else if (args.TONE === SoundToneMenu.LA) {
            tone_freq = noteFreq.A4;
        } else if (args.TONE === SoundToneMenu.TI) {
            tone_freq = noteFreq.B4;
        }

        if (args.BEAT === SoundBeatMenu.ONE_FOURTH) {
            beat_time = 0x01;
        } else if (args.BEAT === SoundBeatMenu.TWO_FOURTH) {
            beat_time = 0x02;
        } else if (args.BEAT === SoundBeatMenu.THREE_FOURTH) {
            beat_time = 0x03;
        } else if (args.BEAT === SoundBeatMenu.FOUR_FOURTH) {
            beat_time = 0x04;
        } else if (args.BEAT === SoundBeatMenu.FIVE_FOURTH) {
            beat_time = 0x05;
        } else if (args.BEAT === SoundBeatMenu.SIX_FOURTH) {
            beat_time = 0x06;
        } else if (args.BEAT === SoundBeatMenu.ROLL_DICE) {
            beat_time = this.rollDice();
        }

        soundAltoData.push(BLECommand.CMD_PLAY_TONE);
        soundAltoData.push((tone_freq & 0xff00) >> 8);
        soundAltoData.push(tone_freq & 0x00ff);
        soundAltoData.push(((beat_time * 500) & 0xff00) >> 8);
        soundAltoData.push((beat_time * 500) & 0xff);

        this._peripheral.commandSyncFlag.soundAltoFlag = true;
        this._peripheral.send(this._peripheral.packCommand(soundAltoData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > ((beat_time * 500) + 100)) {
                    console.log('soundTreble timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.soundAltoFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.soundAltoFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    soundMelody (args) {
        const soundMelodyData = new Array();
        let melody_index = 0x01;
        if (args.MELODY === SoundMelodyMenu.NUM1) {
            melody_index = 0x01;
        } else if (args.MELODY === SoundMelodyMenu.NUM2) {
            melody_index = 0x02;
        } else if (args.MELODY === SoundMelodyMenu.NUM3) {
            melody_index = 0x03;
        } else if (args.MELODY === SoundMelodyMenu.NUM4) {
            melody_index = 0x04;
        } else if (args.MELODY === SoundMelodyMenu.NUM5) {
            melody_index = 0x05;
        } else if (args.MELODY === SoundMelodyMenu.NUM6) {
            melody_index = 0x06;
        } else if (args.MELODY === SoundMelodyMenu.NUM7) {
            melody_index = 0x07;
        } else if (args.MELODY === SoundMelodyMenu.NUM8) {
            melody_index = 0x08;
        } else if (args.MELODY === SoundMelodyMenu.NUM9) {
            melody_index = 0x09;
        } else if (args.MELODY === SoundMelodyMenu.NUM10) {
            melody_index = 0x0a;
        }

        soundMelodyData.push(BLECommand.CMD_PLAY_MUSIC);
        soundMelodyData.push(PalyMusicCommand.PLAY_UNTIL_DONE);
        soundMelodyData.push(melody_index);

        this._peripheral.commandSyncFlag.soundMelodyFlag = true;
        this._peripheral.send(this._peripheral.packCommand(soundMelodyData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 10000) {
                    console.log('soundMelody timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.soundMelodyFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.soundMelodyFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    soundSong (args) {
        const soundSongData = new Array();
        let sound_index = 0x11;
        if (args.SONG === SoundSongMenu.NUM1) {
            sound_index = 0x11;
        } else if (args.SONG === SoundSongMenu.NUM2) {
            sound_index = 0x12;
        } else if (args.SONG === SoundSongMenu.NUM3) {
            sound_index = 0x13;
        } else if (args.SONG === SoundSongMenu.NUM4) {
            sound_index = 0x14;
        } else if (args.SONG === SoundSongMenu.NUM5) {
            sound_index = 0x15;
        } else if (args.SONG === SoundSongMenu.NUM6) {
            sound_index = 0x16;
        } else if (args.SONG === SoundSongMenu.ROLL_DICE) {
            sound_index = 0x10 + this.rollDice();
        }

        soundSongData.push(BLECommand.CMD_PLAY_MUSIC);
        soundSongData.push(PalyMusicCommand.PLAY_UNTIL_DONE);
        soundSongData.push(sound_index);

        this._peripheral.commandSyncFlag.soundSongFlag = true;
        this._peripheral.send(this._peripheral.packCommand(soundSongData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 20000) {
                    console.log('soundSong timeout');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.soundSongFlag = false;
                    resolve();
                }
                if (this._peripheral.commandSyncFlag.soundSongFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    eyeLedSingleSet1 (args) {
        const eyeLedSingleSet1Data = new Array();
        let led_index = 0x03;
        let brightness_level = 0x01;
        let r_value = 0xff;
        let g_value = 0xff;
        let b_value = 0xff;

        if (args.SIDE === LooksSideMenu.LEFT) {
            led_index = 0x01;
        } else if (args.LED_INDEX === LooksSideMenu.RIGHT) {
            led_index = 0x02;
        } else if (args.LED_INDEX === LooksSideMenu.ALL) {
            led_index = 0x03;
        }

        if (args.COLOR_TYPE === ColorTypeMenu.WHITE) {
            r_value = 0xff;
            g_value = 0xff;
            b_value = 0xff;
        } else if (args.COLOR_TYPE === ColorTypeMenu.RED) {
            r_value = 0xff;
            g_value = 0x00;
            b_value = 0x00;
        } else if (args.COLOR_TYPE === ColorTypeMenu.YELLOW) {
            r_value = 0xff;
            g_value = 0xff;
            b_value = 0x00;
        } else if (args.COLOR_TYPE === ColorTypeMenu.GREEN) {
            r_value = 0x00;
            g_value = 0xff;
            b_value = 0x00;
        } else if (args.COLOR_TYPE === ColorTypeMenu.BLUE) {
            r_value = 0x00;
            g_value = 0x00;
            b_value = 0xff;
        } else if (args.COLOR_TYPE === ColorTypeMenu.PURPLE) {
            r_value = 0x80;
            g_value = 0x00;
            b_value = 0xff;
        } else if (args.COLOR_TYPE === ColorTypeMenu.BLACK) {
            r_value = 0x00;
            g_value = 0x00;
            b_value = 0x00;
        }

        if (args.BRIGHTNESS_LEVEL === BrightnessLevelMenu.LEV1) {
            brightness_level = 2;
        } else if (args.BRIGHTNESS_LEVEL === BrightnessLevelMenu.LEV2) {
            brightness_level = 8;
        } else if (args.BRIGHTNESS_LEVEL === BrightnessLevelMenu.LEV3) {
            brightness_level = 27;
        } else if (args.BRIGHTNESS_LEVEL === BrightnessLevelMenu.LEV4) {
            brightness_level = 64;
        } else if (args.BRIGHTNESS_LEVEL === BrightnessLevelMenu.LEV5) {
            brightness_level = 125;
        } else if (args.BRIGHTNESS_LEVEL === BrightnessLevelMenu.LEV6) {
            brightness_level = 216;
        } else if (args.BRIGHTNESS_LEVEL === BrightnessLevelMenu.ROLL_DICE) {
            brightness_level = this.rollDice() * this.rollDice() * this.rollDice();
        }
        eyeLedSingleSet1Data.push(BLECommand.CMD_EYE_LED);
        eyeLedSingleSet1Data.push(led_index);
        eyeLedSingleSet1Data.push(Math.round((r_value * brightness_level) / 216));
        eyeLedSingleSet1Data.push(Math.round((g_value * brightness_level) / 216));
        eyeLedSingleSet1Data.push(Math.round((b_value * brightness_level) / 216));
        this._peripheral.commandSyncFlag.eyeLedSingleSet1Flag = true;
        this._peripheral.send(this._peripheral.packCommand(eyeLedSingleSet1Data));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 2000) {
                    console.log('eyeLedSingleSet1 timeout!');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.eyeLedSingleSet1Flag = false;
                    resolve();
                } else if (this._peripheral.commandSyncFlag.eyeLedSingleSet1Flag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    eyeLedSingleSet2 (args) {
        const eyeLedSingleSet2Data = new Array();
        let led_index = 0x03;
        const rgb = cast.toRgbColorObject(args.COLOR);
        const red = rgb.r;
        const green = rgb.g;
        const blue = rgb.b;

        if (args.SIDE === LooksSideMenu.LEFT) {
            led_index = 0x01;
        } else if (args.LED_INDEX === LooksSideMenu.RIGHT) {
            led_index = 0x02;
        } else if (args.LED_INDEX === LooksSideMenu.ALL) {
            led_index = 0x03;
        }
        eyeLedSingleSet2Data.push(BLECommand.CMD_EYE_LED);
        eyeLedSingleSet2Data.push(led_index);
        eyeLedSingleSet2Data.push(red);
        eyeLedSingleSet2Data.push(green);
        eyeLedSingleSet2Data.push(blue);
        this._peripheral.commandSyncFlag.eyeLedSingleSet2Flag = true;
        this._peripheral.send(this._peripheral.packCommand(eyeLedSingleSet2Data));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 2000) {
                    console.log('eyeLedSingleSet2 timeout!');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.eyeLedSingleSet2Flag = false;
                    resolve();
                } else if (this._peripheral.commandSyncFlag.eyeLedSingleSet2Flag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    eyeLedSingleSet3 (args) {
        const eyeLedSingleSet3Data = new Array();
        const red = MathUtil.clamp(args.RED_VALUE, 0, 255);
        const green = MathUtil.clamp(args.GREEN_VALUE, 0, 255);
        const blue = MathUtil.clamp(args.BLUE_VALUE, 0, 255);
        let led_index = args.LED_INDEX % 3;
        if (led_index === 0) {
            led_index = 0x03;
        }
        eyeLedSingleSet3Data.push(BLECommand.CMD_EYE_LED);
        eyeLedSingleSet3Data.push(led_index);
        eyeLedSingleSet3Data.push(red);
        eyeLedSingleSet3Data.push(green);
        eyeLedSingleSet3Data.push(blue);
        this._peripheral.commandSyncFlag.eyeLedSingleSet3Flag = true;
        this._peripheral.send(this._peripheral.packCommand(eyeLedSingleSet3Data));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 2000) {
                    console.log('eyeLedSingleSet3 timeout!');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.eyeLedSingleSet3Flag = false;
                    resolve();
                } else if (this._peripheral.commandSyncFlag.eyeLedSingleSet3Flag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }

    eyeLedAllOff (args) {
        const eyeLedAllOffData = new Array();
        eyeLedAllOffData.push(BLECommand.CMD_EYE_LED);
        eyeLedAllOffData.push(0x03);
        eyeLedAllOffData.push(0x00);
        eyeLedAllOffData.push(0x00);
        eyeLedAllOffData.push(0x00);
        this._peripheral.commandSyncFlag.eyeLedAllOffFlag = true;
        this._peripheral.send(this._peripheral.packCommand(eyeLedAllOffData));
        return new Promise(resolve => {
            let count = 0;
            const interval = setInterval(() => {
                if (count > 2000) {
                    console.log('eyeLedAllOff timeout!');
                    clearInterval(interval);
                    this._peripheral.commandSyncFlag.eyeLedAllOffFlag = false;
                    resolve();
                } else if (this._peripheral.commandSyncFlag.eyeLedAllOffFlag === false) {
                    clearInterval(interval);
                    resolve();
                }
                count += 10;
            }, 10);
        });
    }
}

module.exports = Scratch3MatataBotBlocks;
