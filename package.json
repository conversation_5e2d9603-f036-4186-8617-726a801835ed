{"_args": [["github:YanMinge/scratch-vm#develop", "/Users/<USER>/Developer/workspace/h5/scratch-demo/scratch-gui-develop"]], "_development": true, "_from": "github:YanMinge/scratch-vm#develop", "_id": "scratch-vm@github:YanMinge/scratch-vm#cbe218caacad184c6e6e745ce6c3a02933ca19d5", "_inBundle": false, "_integrity": "", "_location": "/scratch-vm", "_phantomChildren": {"ajv": "6.10.2", "ajv-keywords": "3.4.1", "domelementtype": "1.3.1", "domhandler": "2.4.2", "domutils": "1.5.1", "entities": "1.1.2", "inherits": "2.0.4", "loader-utils": "1.2.3", "string_decoder": "1.1.1", "util-deprecate": "1.0.2"}, "_requested": {"type": "git", "raw": "github:YanMinge/scratch-vm#develop", "rawSpec": "github:YanMinge/scratch-vm#develop", "saveSpec": "github:YanMinge/scratch-vm#develop", "fetchSpec": null, "gitCommittish": "develop"}, "_requiredBy": ["#DEV:/"], "_resolved": "github:YanMinge/scratch-vm#9c0025560fcea3cc127c8f7cdc66667c71755aea", "_spec": "github:YanMinge/scratch-vm#develop", "_where": "/Users/<USER>/Developer/workspace/h5/scratch-demo/scratch-gui-develop", "author": {"name": "Massachusetts Institute of Technology"}, "browser": "./src/index.js", "bugs": {"url": "https://github.com/YanMinge/scratch-vm/issues"}, "dependencies": {"@vernier/godirect": "1.5.0", "arraybuffer-loader": "^1.0.6", "atob": "2.1.2", "btoa": "1.2.1", "canvas-toBlob": "1.0.0", "decode-html": "2.0.0", "diff-match-patch": "1.0.4", "format-message": "6.2.1", "htmlparser2": "3.10.0", "immutable": "3.8.1", "jszip": "^3.1.5", "minilog": "3.1.0", "nets": "3.2.0", "scratch-parser": "5.0.0", "scratch-sb1-converter": "0.2.7", "scratch-translate-extension-languages": "0.0.20191118205314", "text-encoding": "0.7.0", "worker-loader": "^1.1.1"}, "description": "Virtual Machine for Scratch 3.0", "devDependencies": {"@babel/core": "^7.1.2", "@babel/preset-env": "^7.1.0", "adm-zip": "0.4.11", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.4", "callsite": "^1.0.0", "copy-webpack-plugin": "^4.5.4", "docdash": "^1.0.0", "eslint": "^5.3.0", "eslint-config-scratch": "^5.0.0", "expose-loader": "0.7.5", "file-loader": "^2.0.0", "format-message-cli": "6.2.0", "gh-pages": "^1.2.0", "in-publish": "^2.0.0", "jsdoc": "^3.5.5", "json": "^9.0.4", "lodash.defaultsdeep": "4.6.1", "pngjs": "^3.3.2", "scratch-audio": "latest", "scratch-blocks": "latest", "scratch-l10n": "^3.1.20181129221712", "scratch-render": "latest", "scratch-storage": "^1.1.0", "scratch-svg-renderer": "latest", "script-loader": "0.7.2", "stats.js": "^0.17.0", "tap": "^12.0.1", "tiny-worker": "^2.1.1", "uglifyjs-webpack-plugin": "1.2.7", "webpack": "^4.16.5", "webpack-cli": "^3.1.0", "webpack-dev-server": "^3.1.5"}, "homepage": "https://github.com/YanMinge/scratch-vm#readme", "license": "BSD-3-<PERSON><PERSON>", "main": "./dist/node/scratch-vm.js", "name": "scratch-vm", "repository": {"type": "git", "url": "git+ssh://**************/YanMinge/scratch-vm.git"}, "scripts": {"build": "npm run docs && webpack --progress --colors --bail", "coverage": "tap ./test/{unit,integration}/*.js --coverage --coverage-report=lcov", "deploy": "touch playground/.nojekyll && gh-pages -t -d playground -m \"Build for $(git log --pretty=format:%H -n1)\"", "docs": "jsdoc -c .jsdoc.json", "i18n:push": "tx-push-src scratch-editor extensions translations/core/en.json", "i18n:src": "mkdirp translations/core && format-message extract --out-file translations/core/en.json src/extensions/**/index.js", "lint": "eslint . && format-message lint src/**/*.js", "prepublish": "in-publish && npm run build || not-in-publish", "start": "webpack-dev-server", "tap": "tap ./test/{unit,integration}/*.js", "tap:integration": "tap ./test/integration/*.js", "tap:unit": "tap ./test/unit/*.js", "test": "npm run lint && npm run docs && npm run tap", "version": "json -f package.json -I -e \"this.repository.sha = '$(git log -n1 --pretty=format:%H)'\"", "watch": "webpack --progress --colors --watch"}, "version": "0.2.0"}