module.exports = [
    {
        id: '1ZGd(W8DvU?vI1RN)e0E',
        opcode: 'motion_goto',
        inputs: {
            TO: {
                name: 'TO',
                block: 'Ht(rOKMe0@sB3n4(b3;=',
                shadow: '-C=c-_TI_7d(l3ii2[wh'
            }
        },
        fields: {

        },
        next: 'l.JBk`WcXE+A@i9y1tCU',
        topLevel: true,
        parent: null,
        shadow: false
    },
    {
        id: 'Ht(rOKMe0@sB3n4(b3;=',
        opcode: 'looks_size',
        inputs: {

        },
        fields: {

        },
        next: null,
        topLevel: false,
        parent: '1ZGd(W8DvU?vI1RN)e0E',
        shadow: false
    },
    {
        id: '-C=c-_TI_7d(l3ii2[wh',
        opcode: 'motion_goto_menu',
        inputs: {

        },
        fields: {
            TO: {
                name: 'TO',
                value: '_random_'
            }
        },
        next: null,
        topLevel: false,
        parent: '1ZGd(W8DvU?vI1RN)e0E',
        shadow: true
    },
    {
        id: 'l.JBk`WcXE+A@i9y1tCU',
        opcode: 'sound_stopallsounds',
        inputs: {

        },
        fields: {

        },
        next: null,
        topLevel: false,
        parent: '1ZGd(W8DvU?vI1RN)e0E',
        shadow: false
    }
];
