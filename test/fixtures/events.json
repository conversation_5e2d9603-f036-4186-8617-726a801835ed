{"create": {"workspaceId": "7Luws3lyb*Z98~Kk+IG|", "group": ";OswyM#@%`%,xOrhOXC=", "recordUndo": true, "name": "block", "xml": {"outerHTML": "<block type=\"wedo_motorclockwise\" id=\"z!+#Nqr,_(V=xz0y7a@d\"><value name=\"DURATION\"><shadow type=\"math_number\" id=\"!6Ahqg4f}Ljl}X5Hws?Z\"><field name=\"NUM\">10</field></shadow></value></block>"}, "ids": ["z!+#Nqr,_(V=xz0y7a@d", "!6Ahqg4f}Ljl}X5Hws?Z"]}, "createComment": {"workspaceId": "7Luws3lyb*Z98~Kk+IG|", "group": ";OswyM#@%`%,xOrhOXC=", "recordUndo": true, "name": "block", "xml": {"outerHTML": "<block type=\"wedo_motorclockwise\" id=\"z!+#Nqr,_(V=xz0y7a@d\"><comment id=\"aCommentId\">Some comment text</comment><value name=\"DURATION\"><shadow type=\"math_number\" id=\"!6Ahqg4f}Ljl}X5Hws?Z\"><field name=\"NUM\">10</field></shadow></value></block>"}, "ids": ["z!+#Nqr,_(V=xz0y7a@d", "!6Ahqg4f}Ljl}X5Hws?Z"]}, "createbranch": {"name": "block", "xml": {"outerHTML": "<block type=\"control_forever\" id=\"r9`RpL74T6*SXPKv7}Dq\" x=\"61\" y=\"90\"><statement name=\"SUBSTACK\"><block type=\"control_wait\" id=\"{Rwt[LFtD1-JPAi-qf:.\"><value name=\"DURATION\"><shadow type=\"math_number\" id=\"VMDxt_9SYe5{*eNRe5dZ\"><field name=\"NUM\">1</field></shadow></value></block></statement></block>"}}, "createtwobranches": {"name": "block", "xml": {"outerHTML": "<block type=\"control_if_else\" id=\"8W?lmIY!Tgnh)~0!G#9-\" x=\"87\" y=\"159\"><statement name=\"SUBSTACK\"><block type=\"event_broadcast\" id=\"lgU2GGtwlREuasCB02Vr\"></block></statement><statement name=\"SUBSTACK2\"><block type=\"event_broadcast\" id=\"Gb]N,2P;|J%F?pxSwz(2\"></block></statement></block>"}}, "createtoplevelshadow": {"name": "shadow", "xml": {"outerHTML": "<shadow type=\"math_number\" id=\"z9d57=IUI5se;DBbyug)\"><field name=\"NUM\">4</field></shadow>"}}, "createwithnext": {"name": "block", "xml": {"outerHTML": "<block type=\"wedo_setcolor\" id=\"*CT)7+UKjQIEtUw.OGT6\" x=\"89\" y=\"48\"><next><block type=\"wedo_motorspeed\" id=\"Er*:^o7yYL#dX+5)R^xq\"></block></next></block>"}}, "createinvalid": {"name": "whatever", "xml": {"outerHTML": "<xml></xml>"}}, "createinvalidgrandchild": {"name": "block", "xml": {"outerHTML": "<block type=\"control_forever\" id=\"r9`RpL74T6*SXPKv7}Dq\" x=\"61\" y=\"90\"><next><invalidgrandchild>xxx</invalidgrandchild></next></block>"}}, "createbadxml": {"name": "whatever", "xml": {"outerHTML": "></xml>"}}, "createemptyfield": {"name": "block", "xml": {"outerHTML": "<block type='operator_equals' id='l^H_{8[DDyDW?m)HIt@b' x='100' y='362'><value name='OPERAND1'><shadow type='text' id='Ud@4y]bc./]uv~te?brb'><field name='TEXT'></field></shadow></value><value name='OPERAND2'><shadow type='text' id='p8[y..,[K;~G,k7]N;08'><field name='TEXT'></field></shadow></value></block>"}}, "createvariablewithentity": {"name": "block", "xml": {"outerHTML": "<block type='data_variable' id='/b?}ZGt/N5FmS2yw5GHZ' x='0' y='0'><field name='VARIABLE' id='k-q!YMpHim*lrSX)v(8t' variabletype=''>this &amp; that</field></block>"}}, "createobscuredshadow": {"name": "block", "xml": {"outerHTML": "<block type='operator_add' id='D;MqidqmaN}Dft)y#Bf`' x='80' y='98'><value name='NUM1'><shadow type='math_number' id='F[IFAdLbq8!q25+Nio@i'><field name='NUM'></field></shadow><block type='sensing_answer' id='D~ZQ|BYb1)xw4)8ziI%.'></block</value><value name='NUM2'><shadow type='math_number' id='|Sjv4!*X6;wj?QaCE{-9'><field name='NUM'></field></shadow></value></block>"}}, "createcommentUpdatePosition": {"name": "comment", "type": "comment_create", "commentId": "a comment", "xy": {"x": 10, "y": 20}}, "mockVariableBlock": {"name": "block", "xml": {"outerHTML": "<block type='data_variable' id='a block' x='0' y='0'><field name='VARIABLE' id='mock var id' variabletype=''>a mock variable</field></block>"}}, "mockBroadcastBlock": {"name": "block", "xml": {"outerHTML": "<block type='event_broadcast' id='a broadcast block' x='0' y='0'><value name='BROADCAST_INPUT' id='mock broadcast input'><shadow id='boadcast shadow' type='event_broadcast_menu'><field name='BROADCAST_OPTION' id='mock broadcast message id' variabletype='broadcast-msg'>my message</field></shadow></value></block>"}}, "mockListBlock": {"name": "block", "xml": {"outerHTML": "<block type='data_listcontents' id='another block' x='0' y='0'><field name='LIST' id='mock list id' variabletype=''>a mock list</field></block>"}}}